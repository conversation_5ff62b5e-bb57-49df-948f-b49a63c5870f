import { generateShortCode } from '../utils/generator.js';
import { validateUrl, validateShortCode, validateMinutes } from '../utils/validation.js';
import { addUrl, loadUrls, updateUrl, findByCode } from './storageService.js';
import logger from '../middleware/logger.js';

export const createShortUrl = (url, customCode, minutes) => {
  const urlCheck = validateUrl(url);
  if (!urlCheck.valid) {
    throw new Error(urlCheck.error);
  }

  const existing = loadUrls();
  const codes = existing.map(u => u.shortCode);

  const codeCheck = validateShortCode(customCode, codes);
  if (!codeCheck.valid) {
    throw new Error(codeCheck.error);
  }

  const minutesCheck = validateMinutes(minutes);
  if (!minutesCheck.valid) {
    throw new Error(minutesCheck.error);
  }

  const shortCode = codeCheck.value || generateShortCode(codes);
  const now = new Date();
  const expires = new Date(now.getTime() + minutesCheck.value * 60 * 1000);

  const entry = {
    id: Date.now().toString(),
    originalUrl: urlCheck.value,
    shortCode,
    createdAt: now.toISOString(),
    expiresAt: expires.toISOString(),
    validityMinutes: minutesCheck.value,
    clicks: []
  };

  logger.info(`URL shortened: ${shortCode}`, 'service');
  return addUrl(entry);
};

export const accessUrl = (code) => {
  const entry = findByCode(code);

  if (!entry) {
    throw new Error('URL not found');
  }

  if (new Date() > new Date(entry.expiresAt)) {
    throw new Error('URL has expired');
  }

  const click = {
    timestamp: new Date().toISOString(),
    source: document.referrer || 'direct',
    location: Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown'
  };

  entry.clicks.push(click);
  updateUrl(entry.id, { clicks: entry.clicks });

  logger.info(`URL accessed: ${code}`, 'service');
  return entry;
};

export const getAllUrls = () => loadUrls();

export const getStats = () => {
  const urls = loadUrls();
  const now = new Date();

  return {
    total: urls.length,
    active: urls.filter(u => new Date(u.expiresAt) > now).length,
    clicks: urls.reduce((sum, u) => sum + u.clicks.length, 0)
  };
};
