// URL shortening service
import { generateShortCode } from '../utils/generator.js';
import { validateUrl, validateShortCode, validateValidityMinutes } from '../utils/validation.js';
import { addUrl, loadUrls, updateUrl, getUrlByShortCode } from './storageService.js';
import logger from '../middleware/logger.js';

export const createShortUrl = (originalUrl, customShortCode, validityMinutes) => {
  // Validate inputs
  const urlValidation = validateUrl(originalUrl);
  if (!urlValidation.isValid) {
    throw new Error(urlValidation.error);
  }

  const existingUrls = loadUrls();
  const existingCodes = existingUrls.map(url => url.shortCode);

  const shortCodeValidation = validateShortCode(customShortCode, existingCodes);
  if (!shortCodeValidation.isValid) {
    throw new Error(shortCodeValidation.error);
  }

  const validityValidation = validateValidityMinutes(validityMinutes);
  if (!validityValidation.isValid) {
    throw new Error(validityValidation.error);
  }

  // Generate or use custom short code
  const shortCode = shortCodeValidation.value || generateShortCode(existingCodes);
  
  // Create URL entry
  const now = new Date();
  const expiresAt = new Date(now.getTime() + validityValidation.value * 60 * 1000);
  
  const urlEntry = {
    id: Date.now().toString(),
    originalUrl: urlValidation.value,
    shortCode,
    createdAt: now.toISOString(),
    expiresAt: expiresAt.toISOString(),
    validityMinutes: validityValidation.value,
    clicks: []
  };

  // Save to storage
  const savedUrl = addUrl(urlEntry);
  logger.info('Short URL created', { shortCode, originalUrl: urlValidation.value });
  
  return savedUrl;
};

export const accessShortUrl = (shortCode) => {
  const urlEntry = getUrlByShortCode(shortCode);
  
  if (!urlEntry) {
    logger.warn('Short URL not found', { shortCode });
    throw new Error('Short URL not found');
  }

  // Check if expired
  const now = new Date();
  const expiresAt = new Date(urlEntry.expiresAt);
  
  if (now > expiresAt) {
    logger.warn('Short URL expired', { shortCode, expiresAt: urlEntry.expiresAt });
    throw new Error('Short URL has expired');
  }

  // Record click
  const clickData = {
    timestamp: now.toISOString(),
    source: document.referrer || 'direct',
    location: Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown'
  };

  urlEntry.clicks.push(clickData);
  updateUrl(urlEntry.id, { clicks: urlEntry.clicks });
  
  logger.info('Short URL accessed', { shortCode, originalUrl: urlEntry.originalUrl });
  
  return urlEntry;
};

export const getAllUrls = () => {
  return loadUrls();
};

export const getUrlStats = () => {
  const urls = loadUrls();
  const now = new Date();
  
  const stats = {
    totalUrls: urls.length,
    activeUrls: urls.filter(url => new Date(url.expiresAt) > now).length,
    totalClicks: urls.reduce((sum, url) => sum + url.clicks.length, 0)
  };
  
  return stats;
};
