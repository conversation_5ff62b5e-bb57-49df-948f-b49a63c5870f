import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Container, Typography, Box, Button, Paper, CircularProgress } from '@mui/material';
import { Home, Error } from '@mui/icons-material';
import { accessUrl } from '../services/urlService';
import logger from '../middleware/logger';

function RedirectHandler() {
  const { shortCode } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    handleRedirect();
  }, [shortCode]);

  const handleRedirect = async () => {
    try {
      logger.info(`Attempting to access short URL: ${shortCode}`, 'page');

      const entry = accessUrl(shortCode);
      setRedirecting(true);

      setTimeout(() => {
        window.location.href = entry.originalUrl;
      }, 1000);

    } catch (err) {
      setError(err.message);
      logger.error(`URL access failed for ${shortCode}: ${err.message}`, 'page');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="sm" sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        textAlign: 'center'
      }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Looking up short URL...
        </Typography>
      </Container>
    );
  }

  if (redirecting) {
    return (
      <Container maxWidth="sm" sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        textAlign: 'center'
      }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Redirecting you now...
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          If you're not redirected automatically, please check your browser settings.
        </Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh'
      }}>
        <Paper sx={{ p: 6, textAlign: 'center', width: '100%', maxWidth: 500, boxShadow: 3 }}>
          <Error color="error" sx={{ fontSize: 80, mb: 3 }} />

          <Typography variant="h4" gutterBottom>
            URL Not Found
          </Typography>

          <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
            {error}
          </Typography>

          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            The short URL "/{shortCode}" may have expired or doesn't exist.
          </Typography>

          <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center' }}>
            <Button variant="contained" component={Link} to="/" startIcon={<Home />} size="large">
              Create New URL
            </Button>
            <Button variant="outlined" component={Link} to="/statistics" size="large">
              View Statistics
            </Button>
          </Box>
        </Paper>
      </Container>
    );
  }

  return null;
}

export default RedirectHandler;
