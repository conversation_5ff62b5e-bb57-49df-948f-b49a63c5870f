import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Container, Typography, Box, Button, Paper, CircularProgress } from '@mui/material';
import { Home, Error } from '@mui/icons-material';
import { accessShortUrl } from '../services/urlService';
import logger from '../middleware/logger';

function RedirectHandler() {
  const { shortCode } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    handleRedirect();
  }, [shortCode]);

  const handleRedirect = async () => {
    try {
      logger.info('Attempting to access short URL', { shortCode });
      
      const urlEntry = accessShortUrl(shortCode);
      
      setRedirecting(true);
      
      // Small delay to show the redirect message
      setTimeout(() => {
        window.location.href = urlEntry.originalUrl;
      }, 1500);
      
    } catch (err) {
      setError(err.message);
      logger.error('Failed to access short URL', { shortCode, error: err.message });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Looking up short URL...
        </Typography>
      </Container>
    );
  }

  if (redirecting) {
    return (
      <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Redirecting you now...
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          If you're not redirected automatically, please check your browser settings.
        </Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="sm" sx={{ mt: 8 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Error color="error" sx={{ fontSize: 60, mb: 2 }} />
          
          <Typography variant="h5" gutterBottom>
            URL Not Found
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            {error}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            The short URL "/{shortCode}" may have expired or doesn't exist.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button variant="contained" component={Link} to="/" startIcon={<Home />}>
              Create New URL
            </Button>
            <Button variant="outlined" component={Link} to="/statistics">
              View Statistics
            </Button>
          </Box>
        </Paper>
      </Container>
    );
  }

  return null;
}

export default RedirectHandler;
