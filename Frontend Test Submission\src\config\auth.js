// Authentication configuration for evaluation service
export const AUTH_CONFIG = {
  email: "<EMAIL>",
  name: "<PERSON><PERSON><PERSON>",
  rollNo: "12319077",
  accessCode: "yourAccessCodeFromEmail", // Replace with actual access code
  clientID: "yourClientID", // Replace with actual client ID from registration
  clientSecret: "yourClientSecret" // Replace with actual client secret from registration
};

export const API_BASE_URL = "http://20.244.56.144/eva1uation-service";

// Instructions for setup:
// 1. First register using POST /register with your details
// 2. Save the clientID and clientSecret from the response
// 3. Update the values above with your actual credentials
// 4. The logger will automatically authenticate and send logs to the evaluation service
