class Logger {
  constructor() {
    this.logs = [];
  }

  log(level, message, data = {}) {
    const entry = {
      time: new Date().toISOString(),
      level,
      message,
      data
    };
    this.logs.push(entry);

    try {
      localStorage.setItem('app_logs', JSON.stringify(this.logs.slice(-50)));
    } catch (e) {
      // ignore storage errors
    }
  }

  info(message, data) {
    this.log('INFO', message, data);
  }

  error(message, data) {
    this.log('ERROR', message, data);
  }
}

export default new Logger();
