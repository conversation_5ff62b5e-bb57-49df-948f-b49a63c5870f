import { AUTH_CONFIG, API_BASE_URL } from '../config/auth.js';

class Logger {
  constructor() {
    this.logs = [];
    this.accessToken = null;
    this.apiUrl = API_BASE_URL;
    this.initializeAuth();
  }

  async initializeAuth() {
    // Get auth token for logging
    try {
      const authResponse = await fetch(`${this.apiUrl}/auth`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(AUTH_CONFIG)
      });

      if (authResponse.ok) {
        const authData = await authResponse.json();
        this.accessToken = authData.access_token;
        this.info('Logger authenticated with evaluation service', 'middleware');
      }
    } catch (e) {
      // Fallback to local logging if API fails
      this.info('Using local logging - API authentication failed', 'middleware');
    }
  }

  async sendToAPI(logData) {
    if (!this.accessToken) return;

    try {
      await fetch(`${this.apiUrl}/log`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`
        },
        body: JSON.stringify(logData)
      });
    } catch (e) {
      // Ignore API errors
    }
  }

  log(level, message, packageType = 'component', data = {}) {
    const entry = {
      time: new Date().toISOString(),
      level: level.toLowerCase(),
      message,
      data
    };

    this.logs.push(entry);

    // Store locally
    try {
      localStorage.setItem('app_logs', JSON.stringify(this.logs.slice(-50)));
    } catch (e) {
      // ignore storage errors
    }

    // Send to evaluation API
    this.sendToAPI({
      stack: 'frontend',
      level: level.toLowerCase(),
      package: packageType,
      message
    });
  }

  info(message, packageType = 'component', data = {}) {
    this.log('info', message, packageType, data);
  }

  error(message, packageType = 'component', data = {}) {
    this.log('error', message, packageType, data);
  }

  warn(message, packageType = 'component', data = {}) {
    this.log('warn', message, packageType, data);
  }

  debug(message, packageType = 'component', data = {}) {
    this.log('debug', message, packageType, data);
  }
}

export default new Logger();
