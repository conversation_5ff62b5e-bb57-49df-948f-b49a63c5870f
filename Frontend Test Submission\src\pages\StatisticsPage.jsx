import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  AppBar,
  Toolbar,
  Card,
  CardContent,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import { Home, ExpandMore, Launch } from '@mui/icons-material';
import { getAllUrls, getUrlStats } from '../services/urlService';
import logger from '../middleware/logger';

function StatisticsPage() {
  const [urls, setUrls] = useState([]);
  const [stats, setStats] = useState({ totalUrls: 0, activeUrls: 0, totalClicks: 0 });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const allUrls = getAllUrls();
    const urlStats = getUrlStats();
    setUrls(allUrls);
    setStats(urlStats);
    logger.info('Statistics page loaded', { urlCount: allUrls.length });
  };

  const isExpired = (expiresAt) => {
    return new Date() > new Date(expiresAt);
  };

  const formatDate = (isoString) => {
    return new Date(isoString).toLocaleString();
  };

  const getStatusChip = (url) => {
    if (isExpired(url.expiresAt)) {
      return <Chip label="Expired" color="error" size="small" />;
    }
    return <Chip label="Active" color="success" size="small" />;
  };

  const handleUrlClick = (shortCode, originalUrl) => {
    logger.info('URL clicked from statistics', { shortCode, originalUrl });
    window.open(`/${shortCode}`, '_blank');
  };

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            URL Statistics
          </Typography>
          <Button color="inherit" component={Link} to="/" startIcon={<Home />}>
            Home
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Typography variant="h4" gutterBottom align="center">
          Statistics Dashboard
        </Typography>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total URLs
                </Typography>
                <Typography variant="h4">
                  {stats.totalUrls}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Active URLs
                </Typography>
                <Typography variant="h4" color="success.main">
                  {stats.activeUrls}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Clicks
                </Typography>
                <Typography variant="h4" color="primary">
                  {stats.totalClicks}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* URLs Table */}
        <Paper sx={{ mb: 3 }}>
          <Box sx={{ p: 2 }}>
            <Typography variant="h5" gutterBottom>
              All Shortened URLs
            </Typography>
          </Box>
          
          {urls.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography color="text.secondary">
                No URLs created yet. <Link to="/">Create your first URL</Link>
              </Typography>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Short URL</TableCell>
                    <TableCell>Original URL</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Expires</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Clicks</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {urls.map((url) => (
                    <TableRow key={url.id}>
                      <TableCell>
                        <Typography variant="body2" color="primary">
                          {window.location.origin}/{url.shortCode}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {url.originalUrl}
                        </Typography>
                      </TableCell>
                      <TableCell>{formatDate(url.createdAt)}</TableCell>
                      <TableCell>{formatDate(url.expiresAt)}</TableCell>
                      <TableCell>{getStatusChip(url)}</TableCell>
                      <TableCell>
                        <Chip label={url.clicks.length} variant="outlined" size="small" />
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          startIcon={<Launch />}
                          onClick={() => handleUrlClick(url.shortCode, url.originalUrl)}
                          disabled={isExpired(url.expiresAt)}
                        >
                          Visit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>

        {/* Click Details */}
        {urls.some(url => url.clicks.length > 0) && (
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 2 }}>
              <Typography variant="h5" gutterBottom>
                Click Details
              </Typography>
            </Box>
            
            {urls.filter(url => url.clicks.length > 0).map((url) => (
              <Accordion key={url.id}>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography>{url.shortCode}</Typography>
                    <Chip label={`${url.clicks.length} clicks`} size="small" />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <List dense>
                    {url.clicks.map((click, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={formatDate(click.timestamp)}
                          secondary={`Source: ${click.source} | Location: ${click.location}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </Paper>
        )}
      </Container>
    </>
  );
}

export default StatisticsPage;
