export const validateUrl = (url) => {
  if (!url || !url.trim()) {
    return { valid: false, error: 'URL is required' };
  }

  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return { valid: false, error: 'URL must start with http:// or https://' };
  }

  return { valid: true, value: url.trim() };
};

export const validateShortCode = (code, existing = []) => {
  if (!code || !code.trim()) {
    return { valid: true, value: null };
  }

  const trimmed = code.trim();

  if (trimmed.length < 3 || trimmed.length > 15) {
    return { valid: false, error: 'Code must be 3-15 characters' };
  }

  if (!/^[a-zA-Z0-9]+$/.test(trimmed)) {
    return { valid: false, error: 'Only letters and numbers allowed' };
  }

  if (existing.includes(trimmed)) {
    return { valid: false, error: 'Code already exists' };
  }

  return { valid: true, value: trimmed };
};

export const validateMinutes = (minutes) => {
  if (!minutes) return { valid: true, value: 30 };

  const num = parseInt(minutes);
  if (isNaN(num) || num < 1) {
    return { valid: false, error: 'Must be a positive number' };
  }

  return { valid: true, value: num };
};
