// Simple localStorage service
import logger from '../middleware/logger.js';

const STORAGE_KEY = 'url_shortener_data';

export const saveUrls = (urls) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(urls));
    logger.info('URLs saved to storage', { count: urls.length });
  } catch (error) {
    logger.error('Failed to save URLs', { error: error.message });
  }
};

export const loadUrls = () => {
  try {
    const data = localStorage.getItem(STORAGE_KEY);
    const urls = data ? JSON.parse(data) : [];
    logger.info('URLs loaded from storage', { count: urls.length });
    return urls;
  } catch (error) {
    logger.error('Failed to load URLs', { error: error.message });
    return [];
  }
};

export const addUrl = (urlData) => {
  const urls = loadUrls();
  urls.push(urlData);
  saveUrls(urls);
  return urlData;
};

export const updateUrl = (id, updates) => {
  const urls = loadUrls();
  const index = urls.findIndex(url => url.id === id);
  if (index !== -1) {
    urls[index] = { ...urls[index], ...updates };
    saveUrls(urls);
    return urls[index];
  }
  return null;
};

export const getUrlByShortCode = (shortCode) => {
  const urls = loadUrls();
  return urls.find(url => url.shortCode === shortCode);
};
