import logger from '../middleware/logger.js';

const KEY = 'urls_data';

export const saveUrls = (urls) => {
  try {
    localStorage.setItem(KEY, JSON.stringify(urls));
    logger.info('Saved URLs', { count: urls.length });
  } catch (e) {
    logger.error('Save failed', { error: e.message });
  }
};

export const loadUrls = () => {
  try {
    const data = localStorage.getItem(KEY);
    return data ? JSON.parse(data) : [];
  } catch (e) {
    return [];
  }
};

export const addUrl = (url) => {
  const urls = loadUrls();
  urls.push(url);
  saveUrls(urls);
  return url;
};

export const updateUrl = (id, changes) => {
  const urls = loadUrls();
  const index = urls.findIndex(u => u.id === id);
  if (index >= 0) {
    urls[index] = { ...urls[index], ...changes };
    saveUrls(urls);
    return urls[index];
  }
  return null;
};

export const findByCode = (code) => {
  return loadUrls().find(u => u.shortCode === code);
};
