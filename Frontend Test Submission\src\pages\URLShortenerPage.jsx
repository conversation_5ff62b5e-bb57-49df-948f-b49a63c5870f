import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  TextField,
  Paper,
  Alert,
  IconButton,
  AppBar,
  Toolbar,
  Chip
} from '@mui/material';
import { Add, Delete, ContentCopy, BarChart } from '@mui/icons-material';
import { createShortUrl } from '../services/urlService';
import logger from '../middleware/logger';

function URLShortenerPage() {
  const [urlEntries, setUrlEntries] = useState([
    { originalUrl: '', customShortCode: '', validityMinutes: '' }
  ]);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const addUrlEntry = () => {
    if (urlEntries.length < 5) {
      setUrlEntries([...urlEntries, { originalUrl: '', customShortCode: '', validityMinutes: '' }]);
    }
  };

  const removeUrlEntry = (index) => {
    if (urlEntries.length > 1) {
      setUrlEntries(urlEntries.filter((_, i) => i !== index));
    }
  };

  const updateUrlEntry = (index, field, value) => {
    const newEntries = [...urlEntries];
    newEntries[index][field] = value;
    setUrlEntries(newEntries);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError('');
    setResults([]);

    try {
      const newResults = [];
      
      for (let i = 0; i < urlEntries.length; i++) {
        const entry = urlEntries[i];
        
        if (!entry.originalUrl.trim()) {
          continue; // Skip empty entries
        }

        try {
          const result = createShortUrl(
            entry.originalUrl,
            entry.customShortCode,
            entry.validityMinutes
          );
          
          newResults.push({
            ...result,
            shortUrl: `${window.location.origin}/${result.shortCode}`
          });
        } catch (entryError) {
          throw new Error(`Entry ${i + 1}: ${entryError.message}`);
        }
      }

      if (newResults.length === 0) {
        throw new Error('Please enter at least one URL');
      }

      setResults(newResults);
      logger.info(`Successfully created ${newResults.length} short URLs`, 'page');

    } catch (err) {
      setError(err.message);
      logger.error(`URL creation failed: ${err.message}`, 'page');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    logger.info(`URL copied to clipboard: ${text}`, 'component');
  };

  const formatExpiryDate = (isoString) => {
    return new Date(isoString).toLocaleString();
  };

  return (
    <>
      <AppBar position="static" sx={{ bgcolor: '#1b5e20' }}>
        <Toolbar>
          <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            🔗 URL Shortener
          </Typography>
          <Button
            color="inherit"
            component={Link}
            to="/statistics"
            startIcon={<BarChart />}
            sx={{
              bgcolor: 'rgba(255,255,255,0.1)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' },
              borderRadius: 2
            }}
          >
            Statistics
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="md" sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        py: 4
      }}>
        <Typography variant="h3" gutterBottom align="center" sx={{ mb: 2 }}>
          URL Shortener
        </Typography>

        <Typography variant="h6" color="text.secondary" align="center" sx={{ mb: 6 }}>
          Create up to 5 shortened URLs at once
        </Typography>

        <Paper sx={{
          p: 4,
          width: '100%',
          maxWidth: 700,
          boxShadow: '0 8px 32px rgba(46, 125, 50, 0.12)',
          border: '1px solid #e8f5e8',
          borderRadius: 3
        }}>
          {urlEntries.map((entry, index) => (
            <Box key={index} sx={{
              mb: 4,
              p: 3,
              border: '2px solid #c8e6c9',
              borderRadius: 3,
              bgcolor: '#f1f8e9',
              '&:hover': { borderColor: '#4caf50' }
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">URL {index + 1}</Typography>
                {urlEntries.length > 1 && (
                  <IconButton onClick={() => removeUrlEntry(index)} color="error" sx={{ ml: 'auto' }}>
                    <Delete />
                  </IconButton>
                )}
              </Box>

              <TextField
                fullWidth
                label="Original Long URL"
                placeholder="https://example.com/very/long/url"
                value={entry.originalUrl}
                onChange={(e) => updateUrlEntry(index, 'originalUrl', e.target.value)}
                sx={{ mb: 2 }}
                required
              />

              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <TextField
                  label="Custom Shortcode (optional)"
                  placeholder="mycode123"
                  value={entry.customShortCode}
                  onChange={(e) => updateUrlEntry(index, 'customShortCode', e.target.value)}
                  sx={{ flex: 1 }}
                  helperText="Alphanumeric characters only"
                />

                <TextField
                  label="Validity (minutes)"
                  type="number"
                  placeholder="30"
                  value={entry.validityMinutes}
                  onChange={(e) => updateUrlEntry(index, 'validityMinutes', e.target.value)}
                  sx={{ width: 180 }}
                  helperText="Default: 30 minutes"
                />
              </Box>
            </Box>
          ))}

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 3 }}>
            {urlEntries.length < 5 && (
              <Button variant="outlined" onClick={addUrlEntry} startIcon={<Add />}>
                Add Another URL
              </Button>
            )}

            <Button
              variant="contained"
              onClick={handleSubmit}
              disabled={loading}
              size="large"
              sx={{
                bgcolor: '#2e7d32',
                '&:hover': { bgcolor: '#1b5e20' },
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 'bold'
              }}
            >
              {loading ? '🔄 Shortening...' : '✨ Shorten URLs'}
            </Button>
          </Box>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mt: 4, width: '100%', maxWidth: 700 }}>
            {error}
          </Alert>
        )}

        {results.length > 0 && (
          <Paper sx={{
            p: 4,
            mt: 4,
            width: '100%',
            maxWidth: 700,
            boxShadow: '0 8px 32px rgba(255, 111, 0, 0.12)',
            border: '1px solid #fff3e0',
            borderRadius: 3
          }}>
            <Typography variant="h4" gutterBottom align="center" sx={{ mb: 3, color: '#e65100' }}>
              🎉 Your Shortened URLs
            </Typography>

            {results.map((result, index) => (
              <Box key={result.id} sx={{
                mb: 3,
                p: 3,
                bgcolor: '#fff8e1',
                borderRadius: 3,
                textAlign: 'center',
                border: '2px solid #ffcc02',
                '&:hover': {
                  borderColor: '#ff6f00',
                  transform: 'translateY(-2px)',
                  transition: 'all 0.2s ease'
                }
              }}>
                <Typography variant="body1" color="text.secondary" gutterBottom sx={{ mb: 2 }}>
                  Original: {result.originalUrl}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'center', mb: 2 }}>
                  <Typography variant="h5" color="primary" sx={{ fontWeight: 'bold' }}>
                    {result.shortUrl}
                  </Typography>
                  <IconButton size="small" onClick={() => copyToClipboard(result.shortUrl)} color="primary">
                    <ContentCopy />
                  </IconButton>
                </Box>

                <Typography variant="body2" color="text.secondary">
                  Expires: {formatExpiryDate(result.expiresAt)}
                </Typography>
              </Box>
            ))}
          </Paper>
        )}
      </Container>
    </>
  );
}

export default URLShortenerPage;
