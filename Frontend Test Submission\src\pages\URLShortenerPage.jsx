import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  TextField,
  Paper,
  Alert,
  IconButton,
  AppBar,
  Toolbar,
  Chip
} from '@mui/material';
import { Add, Delete, ContentCopy, BarChart } from '@mui/icons-material';
import { createShortUrl } from '../services/urlService';
import logger from '../middleware/logger';

function URLShortenerPage() {
  const [urlEntries, setUrlEntries] = useState([
    { originalUrl: '', customShortCode: '', validityMinutes: '' }
  ]);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const addUrlEntry = () => {
    if (urlEntries.length < 5) {
      setUrlEntries([...urlEntries, { originalUrl: '', customShortCode: '', validityMinutes: '' }]);
      logger.info('URL entry added', { totalEntries: urlEntries.length + 1 });
    }
  };

  const removeUrlEntry = (index) => {
    if (urlEntries.length > 1) {
      const newEntries = urlEntries.filter((_, i) => i !== index);
      setUrlEntries(newEntries);
      logger.info('URL entry removed', { index, totalEntries: newEntries.length });
    }
  };

  const updateUrlEntry = (index, field, value) => {
    const newEntries = [...urlEntries];
    newEntries[index][field] = value;
    setUrlEntries(newEntries);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError('');
    setResults([]);

    try {
      const newResults = [];
      
      for (let i = 0; i < urlEntries.length; i++) {
        const entry = urlEntries[i];
        
        if (!entry.originalUrl.trim()) {
          continue; // Skip empty entries
        }

        try {
          const result = createShortUrl(
            entry.originalUrl,
            entry.customShortCode,
            entry.validityMinutes
          );
          
          newResults.push({
            ...result,
            shortUrl: `${window.location.origin}/${result.shortCode}`
          });
        } catch (entryError) {
          logger.error('Failed to create short URL', { entry, error: entryError.message });
          throw new Error(`Entry ${i + 1}: ${entryError.message}`);
        }
      }

      if (newResults.length === 0) {
        throw new Error('Please enter at least one URL');
      }

      setResults(newResults);
      logger.info('URLs shortened successfully', { count: newResults.length });
      
    } catch (err) {
      setError(err.message);
      logger.error('URL shortening failed', { error: err.message });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    logger.info('URL copied to clipboard', { url: text });
  };

  const formatExpiryDate = (isoString) => {
    return new Date(isoString).toLocaleString();
  };

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            URL Shortener
          </Typography>
          <Button color="inherit" component={Link} to="/statistics" startIcon={<BarChart />}>
            Statistics
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="md" sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        py: 4
      }}>
        <Typography variant="h3" gutterBottom align="center" sx={{ mb: 2 }}>
          URL Shortener
        </Typography>

        <Typography variant="h6" color="text.secondary" align="center" sx={{ mb: 6 }}>
          Create up to 5 shortened URLs at once
        </Typography>

        <Paper sx={{ p: 4, width: '100%', maxWidth: 700, boxShadow: 3 }}>
          {urlEntries.map((entry, index) => (
            <Box key={index} sx={{ mb: 4, p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">URL {index + 1}</Typography>
                {urlEntries.length > 1 && (
                  <IconButton onClick={() => removeUrlEntry(index)} color="error" sx={{ ml: 'auto' }}>
                    <Delete />
                  </IconButton>
                )}
              </Box>

              <TextField
                fullWidth
                label="Original Long URL"
                placeholder="https://example.com/very/long/url"
                value={entry.originalUrl}
                onChange={(e) => updateUrlEntry(index, 'originalUrl', e.target.value)}
                sx={{ mb: 2 }}
                required
              />

              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <TextField
                  label="Custom Shortcode (optional)"
                  placeholder="mycode123"
                  value={entry.customShortCode}
                  onChange={(e) => updateUrlEntry(index, 'customShortCode', e.target.value)}
                  sx={{ flex: 1 }}
                  helperText="Alphanumeric characters only"
                />

                <TextField
                  label="Validity (minutes)"
                  type="number"
                  placeholder="30"
                  value={entry.validityMinutes}
                  onChange={(e) => updateUrlEntry(index, 'validityMinutes', e.target.value)}
                  sx={{ width: 180 }}
                  helperText="Default: 30 minutes"
                />
              </Box>
            </Box>
          ))}

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 3 }}>
            {urlEntries.length < 5 && (
              <Button variant="outlined" onClick={addUrlEntry} startIcon={<Add />}>
                Add Another URL
              </Button>
            )}

            <Button
              variant="contained"
              onClick={handleSubmit}
              disabled={loading}
              size="large"
            >
              {loading ? 'Shortening...' : 'Shorten URLs'}
            </Button>
          </Box>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mt: 4, width: '100%', maxWidth: 700 }}>
            {error}
          </Alert>
        )}

        {results.length > 0 && (
          <Paper sx={{ p: 4, mt: 4, width: '100%', maxWidth: 700, boxShadow: 3 }}>
            <Typography variant="h4" gutterBottom align="center" sx={{ mb: 3 }}>
              Your Shortened URLs
            </Typography>

            {results.map((result, index) => (
              <Box key={result.id} sx={{
                mb: 3,
                p: 3,
                bgcolor: 'grey.50',
                borderRadius: 2,
                textAlign: 'center',
                border: '1px solid #e0e0e0'
              }}>
                <Typography variant="body1" color="text.secondary" gutterBottom sx={{ mb: 2 }}>
                  Original: {result.originalUrl}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'center', mb: 2 }}>
                  <Typography variant="h5" color="primary" sx={{ fontWeight: 'bold' }}>
                    {result.shortUrl}
                  </Typography>
                  <IconButton size="small" onClick={() => copyToClipboard(result.shortUrl)} color="primary">
                    <ContentCopy />
                  </IconButton>
                </Box>

                <Typography variant="body2" color="text.secondary">
                  Expires: {formatExpiryDate(result.expiresAt)}
                </Typography>
              </Box>
            ))}
          </Paper>
        )}
      </Container>
    </>
  );
}

export default URLShortenerPage;
