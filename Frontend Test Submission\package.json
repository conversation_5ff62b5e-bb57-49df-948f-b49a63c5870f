{"name": "my-url-shortener", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "lint": "eslint .", "preview": "vite preview --port 3000"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@mui/material": "^6.1.9", "@mui/icons-material": "^6.1.9", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "react-router-dom": "^7.1.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}